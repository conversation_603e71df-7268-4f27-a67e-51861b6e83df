<template>
  <view class="performance-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">业绩管理</view>
      <view class="nav-subtitle">查看团队业绩表现</view>
    </view>

    <!-- 统计信息卡片 -->
    <view class="stats-card">
      <view class="stats-item">
        <view class="stats-value">{{ performanceObj.myProxy ? performanceObj.myProxy.length : 0 }}</view>
        <view class="stats-label">代理个数</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">{{ performanceObj.self ? performanceObj.self.money : 0 }} ￥</view>
        <view class="stats-label">个人业绩</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-value">{{ sum }} ￥</view>
        <view class="stats-label">总体业绩</view>
      </view>
    </view>

    <!-- 内容区 -->
    <view class="content-wrapper">
      <view v-if="showList.length === 0" class="empty-state">
        <view class="empty-icon">📊</view>
        <view class="empty-text">{{ msg }}</view>
      </view>

      <view class="proxy-list">
        <view class="proxy-card" v-for="(data, index) in showList" :key="index" @click="toProxyDetail(data)">
          <!-- 代理头部 -->
          <view class="proxy-header">
            <view class="proxy-avatar">
              <image :src="data.headPic || '/static/photo.png'" class="avatar-image"></image>
            </view>
            <view class="proxy-basic-info">
              <view class="proxy-name">{{ data.userName }}</view>
              <view class="proxy-date">注册日期：{{ formatTiem(data.createTime) }}</view>
            </view>
            <view class="arrow-icon">›</view>
          </view>

          <!-- 代理详细信息 -->
          <view class="proxy-info">
            <view class="info-row">
              <view class="info-label">手机号</view>
              <view class="info-value">{{ data.phone }}</view>
            </view>
            <view class="info-row">
              <view class="info-label">营业额</view>
              <view class="info-value info-price">{{ data.money === null ? "0.00" : data.money.toFixed(2) }} ￥</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  showPerformance,
  showProxyPerformance,
} from "@/subpackages/proxy/api/proxyPerformance.js";
import { getDateByDay } from "@/utils/dateUtil.js";
import { isLogin, isProxyLogin } from "@/utils/auth.js";

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {
        ohtherProxy: [],
        self: {
          money: 0,
        },
        myProxy: [],
      },
      showList: [],
      msg: "加载中",
      sum: 0,
      dateRange: [],
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.dateRange = [start, end];
      }
    },
    showPerformance() {
      showProxyPerformance().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        this.performanceObj = res.data.data;
        this.index = 0;
        this.showList = this.performanceObj.myProxy;
        let sum = 0;
        this.performanceObj.myProxy.forEach((i) => {
          if (i.money != null) {
            sum += i.money;
          }
        });
        sum += this.performanceObj.self.money;
        this.sum = sum.toFixed(2);
        this.msg = "暂无数据";
      });
    },
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },

    toProxyDetail(proxy) {
      const data = {
        proxy,
        range: this.dateRange.map((o = "") => o.replaceAll("-", "/")),
        checkNav: this.checkNav,
      };
      uni.navigateTo({
        url:
          "/subpackages/proxy/pages/proxyProxyPerformance/proxyPerformance?data=" +
          JSON.stringify(data),
      });
    },
  },
  onLoad() {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    this.showPerformance();
    const start = toLocaleDateString(getDateByDay(0));
    this.dateRange = [start, start];
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.performance-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 统计信息卡片 */
.stats-card {
  background: white;
  margin: 20px;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  justify-content: space-around;
  align-items: center;

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-value {
      font-size: 22px;
      font-weight: 600;
      color: #00d4aa;
      margin-bottom: 8px;
      letter-spacing: 0.5px;
    }

    .stats-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }

  .stats-divider {
    width: 1px;
    height: 50px;
    background: linear-gradient(180deg, transparent 0%, #e0e0e0 50%, transparent 100%);
    margin: 0 20px;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 16px;
    color: #999;
    font-weight: 500;
  }
}

/* 代理列表 */
.proxy-list {
  .proxy-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 代理头部 */
    .proxy-header {
      display: flex;
      align-items: center;
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .proxy-avatar {
        margin-right: 16px;

        .avatar-image {
          width: 60px;
          height: 60px;
          border-radius: 16px;
          border: 3px solid #00d4aa;
          object-fit: cover;
        }
      }

      .proxy-basic-info {
        flex: 1;

        .proxy-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 6px;
          line-height: 1.4;
        }

        .proxy-date {
          font-size: 13px;
          color: #666;
          font-weight: 500;
        }
      }

      .arrow-icon {
        font-size: 20px;
        color: #00d4aa;
        font-weight: 600;
      }
    }

    /* 代理信息 */
    .proxy-info {
      padding: 16px 20px 20px;

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          flex: 1;
          text-align: right;
          word-break: break-all;
        }

        .info-price {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
</style>
